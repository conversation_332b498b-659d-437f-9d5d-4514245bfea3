package slb

import (
	"context"
	"errors"

	"github.com/Azure/azure-sdk-for-go/services/network/mgmt/2022-07-01/network"
	"github.com/Azure/go-autorest/autorest/to"
	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
	"go.uber.org/mock/gomock"

	"go.goms.io/aks/rp/devinfraservices/toggles/rollout"
	"go.goms.io/aks/rp/devinfraservices/toggles/rollout/fake"
	hcpEnums "go.goms.io/aks/rp/protos/hcp/types/enums/v1"
	hcpAgentPool "go.goms.io/aks/rp/protos/hcp/types/agentpool/v1"
	"go.goms.io/aks/rp/resourceprovider/server/microsoft.com/asyncoperationsprocessor/rawgoalretriever/mock_rawgoalretriever"
	rpcommonconsts "go.goms.io/aks/rp/resourceprovider/server/microsoft.com/common/consts"
	"go.goms.io/aks/rp/resourceprovider/server/microsoft.com/containerservice/flags"
	"go.goms.io/aks/rp/resourceprovider/server/microsoft.com/goalresolvers"
	"go.goms.io/aks/rp/resourceprovider/server/microsoft.com/reconcilers/outbound/agentpool/mock_agentpool"
	"go.goms.io/aks/rp/resourceprovider/sharedlib/azureresources/mock_azureresources"
	"go.goms.io/aks/rp/toolkit/apierror"
	"go.goms.io/aks/rp/toolkit/azureclients/loadbalancer/mock_loadbalancer"
	"go.goms.io/aks/rp/toolkit/azureclients/publicipaddress/mock_publicipaddress"
	cgerror "go.goms.io/aks/rp/toolkit/categorizederror"
	"go.goms.io/aks/rp/toolkit/log"
)

var _ = Describe("SLB Excluded Agent Pools", func() {
	var (
		mockCtrl *gomock.Controller
		ctx      context.Context
		logger   *log.Logger

		lbClient              *mock_loadbalancer.MockInterface
		vmssReconciler        *mock_agentpool.MockAgentPoolLBBackendpoolReconciler
		vmReconciler          *mock_agentpool.MockAgentPoolLBBackendpoolReconciler
		vmReconcilerTrack2    *mock_agentpool.MockAgentPoolLBBackendpoolReconciler
		pipClient             *mock_publicipaddress.MockInterface
		pipClientTrack2       *mock_azureresources.MockPublicIPAddressInterface
		agentPoolsRetriever   *mock_rawgoalretriever.MockAgentPoolsInterface
		outboundConn          *slbOutBoundReconciler
	)

	BeforeEach(func() {
		mockCtrl = gomock.NewController(GinkgoT())
		lbClient = mock_loadbalancer.NewMockInterface(mockCtrl)
		vmssReconciler = mock_agentpool.NewMockAgentPoolLBBackendpoolReconciler(mockCtrl)
		vmReconciler = mock_agentpool.NewMockAgentPoolLBBackendpoolReconciler(mockCtrl)
		vmReconcilerTrack2 = mock_agentpool.NewMockAgentPoolLBBackendpoolReconciler(mockCtrl)
		pipClient = mock_publicipaddress.NewMockInterface(mockCtrl)
		pipClientTrack2 = mock_azureresources.NewMockPublicIPAddressInterface(mockCtrl)
		agentPoolsRetriever = mock_rawgoalretriever.NewMockAgentPoolsInterface(mockCtrl)
		logger = log.InitializeTestLogger()
		ctx = log.WithLogger(context.Background(), logger)
		apiTracking := log.NewAPITrackingFromParametersMap(nil)
		ctx = log.WithAPITracking(ctx, apiTracking)

		toggle := fake.New()
		flag := flags.NewFlags(toggle, &rollout.Entity{})
		outboundConn = &slbOutBoundReconciler{
			vmssReconciler:       vmssReconciler,
			vmReconciler:         vmReconciler,
			vmReconcilerTrack2:   vmReconcilerTrack2,
			loadbalancerClient:   lbClient,
			publicIPClient:       pipClient,
			publicIPClientTrack2: pipClientTrack2,
			agentPoolsRetriever:  agentPoolsRetriever,
			opt:                  flag,
		}
	})

	AfterEach(func() {
		mockCtrl.Finish()
	})

	Context("getExcludedAgentPoolNames", func() {
		When("no agent pools exist", func() {
			It("should return empty map", func() {
				agentPoolsRetriever.EXPECT().ListAgentPools(ctx, false).Return([]*hcpAgentPool.AgentPoolResource{}, nil).Times(1)

				excludedPools, err := outboundConn.getExcludedAgentPoolNames(ctx)

				Expect(err).To(BeNil())
				Expect(excludedPools).NotTo(BeNil())
				Expect(len(excludedPools)).To(Equal(0))
			})
		})

		When("agent pools exist but none have exclude label", func() {
			It("should return empty map", func() {
				agentPools := []*hcpAgentPool.AgentPoolResource{
					{
						Properties: &hcpAgentPool.AgentPoolProfile{
							Name: "pool1",
							CustomNodeLabels: map[string]string{
								"some-other-label": "value",
							},
						},
					},
					{
						Properties: &hcpAgentPool.AgentPoolProfile{
							Name: "pool2",
							// No custom node labels
						},
					},
				}
				agentPoolsRetriever.EXPECT().ListAgentPools(ctx, false).Return(agentPools, nil).Times(1)

				excludedPools, err := outboundConn.getExcludedAgentPoolNames(ctx)

				Expect(err).To(BeNil())
				Expect(excludedPools).NotTo(BeNil())
				Expect(len(excludedPools)).To(Equal(0))
			})
		})

		When("some agent pools have exclude label", func() {
			It("should return map with excluded pool names", func() {
				agentPools := []*hcpAgentPool.AgentPoolResource{
					{
						Properties: &hcpAgentPool.AgentPoolProfile{
							Name: "pool1",
							CustomNodeLabels: map[string]string{
								rpcommonconsts.NodeLabelExcludeFromLoadBalancerInbound: "true",
							},
						},
					},
					{
						Properties: &hcpAgentPool.AgentPoolProfile{
							Name: "pool2",
							CustomNodeLabels: map[string]string{
								"some-other-label": "value",
							},
						},
					},
					{
						Properties: &hcpAgentPool.AgentPoolProfile{
							Name: "pool3",
							CustomNodeLabels: map[string]string{
								rpcommonconsts.NodeLabelExcludeFromLoadBalancerInbound: "",
								"another-label": "value",
							},
						},
					},
				}
				agentPoolsRetriever.EXPECT().ListAgentPools(ctx, false).Return(agentPools, nil).Times(1)

				excludedPools, err := outboundConn.getExcludedAgentPoolNames(ctx)

				Expect(err).To(BeNil())
				Expect(excludedPools).NotTo(BeNil())
				Expect(len(excludedPools)).To(Equal(2))
				Expect(excludedPools).To(HaveKey("pool1"))
				Expect(excludedPools).To(HaveKey("pool3"))
				Expect(excludedPools).NotTo(HaveKey("pool2"))
			})
		})

		When("agent pool has nil properties", func() {
			It("should skip that agent pool", func() {
				agentPools := []*hcpAgentPool.AgentPoolResource{
					{
						Properties: nil,
					},
					{
						Properties: &hcpAgentPool.AgentPoolProfile{
							Name: "pool2",
							CustomNodeLabels: map[string]string{
								rpcommonconsts.NodeLabelExcludeFromLoadBalancerInbound: "true",
							},
						},
					},
				}
				agentPoolsRetriever.EXPECT().ListAgentPools(ctx, false).Return(agentPools, nil).Times(1)

				excludedPools, err := outboundConn.getExcludedAgentPoolNames(ctx)

				Expect(err).To(BeNil())
				Expect(excludedPools).NotTo(BeNil())
				Expect(len(excludedPools)).To(Equal(1))
				Expect(excludedPools).To(HaveKey("pool2"))
			})
		})

		When("agent pool has nil custom node labels", func() {
			It("should skip that agent pool", func() {
				agentPools := []*hcpAgentPool.AgentPoolResource{
					{
						Properties: &hcpAgentPool.AgentPoolProfile{
							Name:             "pool1",
							CustomNodeLabels: nil,
						},
					},
					{
						Properties: &hcpAgentPool.AgentPoolProfile{
							Name: "pool2",
							CustomNodeLabels: map[string]string{
								rpcommonconsts.NodeLabelExcludeFromLoadBalancerInbound: "true",
							},
						},
					},
				}
				agentPoolsRetriever.EXPECT().ListAgentPools(ctx, false).Return(agentPools, nil).Times(1)

				excludedPools, err := outboundConn.getExcludedAgentPoolNames(ctx)

				Expect(err).To(BeNil())
				Expect(excludedPools).NotTo(BeNil())
				Expect(len(excludedPools)).To(Equal(1))
				Expect(excludedPools).To(HaveKey("pool2"))
			})
		})

		When("ListAgentPools returns error", func() {
			It("should return error", func() {
				expectedError := &cgerror.CategorizedError{
					Category:    apierror.InternalError,
					Code:        hcpEnums.ErrorCode_InternalOperationError,
					OriginError: errors.New("failed to list agent pools"),
				}
				agentPoolsRetriever.EXPECT().ListAgentPools(ctx, false).Return(nil, expectedError).Times(1)

				excludedPools, err := outboundConn.getExcludedAgentPoolNames(ctx)

				Expect(err).NotTo(BeNil())
				Expect(err).To(Equal(expectedError))
				Expect(excludedPools).To(BeNil())
			})
		})
	})

	Context("EnsureOutboundResources with excluded agent pools", func() {
		var (
			computeSubscriptionID string
			networkSubscriptionID string
			nodeResourceGroupName string
			location              string
			clusterName           string
		)

		BeforeEach(func() {
			computeSubscriptionID = "b8c5d784-96a1-4e65-9acb-9246b26c8888"
			networkSubscriptionID = "b8c5d784-96a1-4e65-9acb-9246b26c8888"
			nodeResourceGroupName = "mc_testingrg_testcluster"
			location = "westus"
			clusterName = "testcluster"
		})

		When("agent pools with exclude label exist", func() {
			It("should pass excluded agent pool names to reconcilers", func() {
				goal := createTestGoal(computeSubscriptionID, networkSubscriptionID, nodeResourceGroupName, location, clusterName)

				agentPools := []*hcpAgentPool.AgentPoolResource{
					{
						Properties: &hcpAgentPool.AgentPoolProfile{
							Name: "excluded-pool",
							CustomNodeLabels: map[string]string{
								rpcommonconsts.NodeLabelExcludeFromLoadBalancerInbound: "true",
							},
						},
					},
					{
						Properties: &hcpAgentPool.AgentPoolProfile{
							Name: "normal-pool",
							CustomNodeLabels: map[string]string{
								"some-other-label": "value",
							},
						},
					},
				}

				lbClient.EXPECT().GetLoadBalancer(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
				lbClient.EXPECT().CreateOrUpdateLoadBalancer(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(createTestLoadBalancer(), nil).Times(1)
				agentPoolsRetriever.EXPECT().ListAgentPools(ctx, false).Return(agentPools, nil).Times(1)

				vmssReconciler.EXPECT().AssociateLBBackendpool(gomock.Any(), computeSubscriptionID, networkSubscriptionID, nodeResourceGroupName, gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(
					func(ctx context.Context, computeSubscriptionID, networkSubscriptionID, resourceGroupName string, backendpoolIDs map[string]struct{}, backendpoolIDsIPV6 map[string]struct{}, excludedAgentPoolNames map[string]struct{}) *cgerror.CategorizedError {
						Expect(len(excludedAgentPoolNames)).To(Equal(1))
						Expect(excludedAgentPoolNames).To(HaveKey("excluded-pool"))
						Expect(excludedAgentPoolNames).NotTo(HaveKey("normal-pool"))
						return nil
					}).Times(1)

				vmReconciler.EXPECT().AssociateLBBackendpool(gomock.Any(), computeSubscriptionID, networkSubscriptionID, nodeResourceGroupName, gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(
					func(ctx context.Context, computeSubscriptionID, networkSubscriptionID, resourceGroupName string, backendpoolIDs map[string]struct{}, backendpoolIDsIPV6 map[string]struct{}, excludedAgentPoolNames map[string]struct{}) *cgerror.CategorizedError {
						Expect(len(excludedAgentPoolNames)).To(Equal(1))
						Expect(excludedAgentPoolNames).To(HaveKey("excluded-pool"))
						return nil
					}).Times(1)

				err := outboundConn.EnsureOutboundResources(ctx, goal, nil, nil)

				Expect(err).To(BeNil())
			})
		})

		When("getExcludedAgentPoolNames returns error", func() {
			It("should return error", func() {
				goal := createTestGoal(computeSubscriptionID, networkSubscriptionID, nodeResourceGroupName, location, clusterName)

				expectedError := &cgerror.CategorizedError{
					Category:    apierror.InternalError,
					Code:        hcpEnums.ErrorCode_InternalOperationError,
					OriginError: errors.New("failed to list agent pools"),
				}

				lbClient.EXPECT().GetLoadBalancer(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
				lbClient.EXPECT().CreateOrUpdateLoadBalancer(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(createTestLoadBalancer(), nil).Times(1)
				agentPoolsRetriever.EXPECT().ListAgentPools(ctx, false).Return(nil, expectedError).Times(1)

				err := outboundConn.EnsureOutboundResources(ctx, goal, nil, nil)

				Expect(err).NotTo(BeNil())
				Expect(err).To(Equal(expectedError))
			})
		})

		When("no agent pools have exclude label", func() {
			It("should pass empty excluded agent pool names to reconcilers", func() {
				goal := createTestGoal(computeSubscriptionID, networkSubscriptionID, nodeResourceGroupName, location, clusterName)

				agentPools := []*hcpAgentPool.AgentPoolResource{
					{
						Properties: &hcpAgentPool.AgentPoolProfile{
							Name: "normal-pool1",
							CustomNodeLabels: map[string]string{
								"some-label": "value",
							},
						},
					},
					{
						Properties: &hcpAgentPool.AgentPoolProfile{
							Name: "normal-pool2",
						},
					},
				}

				lbClient.EXPECT().GetLoadBalancer(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
				lbClient.EXPECT().CreateOrUpdateLoadBalancer(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(createTestLoadBalancer(), nil).Times(1)
				agentPoolsRetriever.EXPECT().ListAgentPools(ctx, false).Return(agentPools, nil).Times(1)

				vmssReconciler.EXPECT().AssociateLBBackendpool(gomock.Any(), computeSubscriptionID, networkSubscriptionID, nodeResourceGroupName, gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(
					func(ctx context.Context, computeSubscriptionID, networkSubscriptionID, resourceGroupName string, backendpoolIDs map[string]struct{}, backendpoolIDsIPV6 map[string]struct{}, excludedAgentPoolNames map[string]struct{}) *cgerror.CategorizedError {
						Expect(len(excludedAgentPoolNames)).To(Equal(0))
						return nil
					}).Times(1)

				vmReconciler.EXPECT().AssociateLBBackendpool(gomock.Any(), computeSubscriptionID, networkSubscriptionID, nodeResourceGroupName, gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(
					func(ctx context.Context, computeSubscriptionID, networkSubscriptionID, resourceGroupName string, backendpoolIDs map[string]struct{}, backendpoolIDsIPV6 map[string]struct{}, excludedAgentPoolNames map[string]struct{}) *cgerror.CategorizedError {
						Expect(len(excludedAgentPoolNames)).To(Equal(0))
						return nil
					}).Times(1)

				err := outboundConn.EnsureOutboundResources(ctx, goal, nil, nil)

				Expect(err).To(BeNil())
			})
		})
	})
})

// Helper functions for creating test data
func createTestGoal(computeSubscriptionID, networkSubscriptionID, nodeResourceGroupName, location, clusterName string) *goalresolvers.OutboundGoal {
	return &goalresolvers.OutboundGoal{
		ManagedClusterSubscriptionID:    computeSubscriptionID,
		NetworkSubscriptionID:           networkSubscriptionID,
		NodeResourceGroupName:           nodeResourceGroupName,
		Location:                        location,
		ManagedClusterName:              clusterName,
		ManagedClusterResourceGroupName: "testingrg_testcluster",
		EnableIPv6:                      false,
		ManagedSLBGoal: &goalresolvers.ManagedSLBGoal{
			OutboundRuleAllocatedOutboundPorts: 0,
			OutboundRuleIdleTimeoutInMinutes:   30,
			BackendPoolType:                    rpcommonconsts.LBBackendPoolTypeNodeIPConfiguration,
		},
	}
}

func createTestLoadBalancer() *network.LoadBalancer {
	return &network.LoadBalancer{
		Name:     to.StringPtr("kubernetes"),
		Location: to.StringPtr("westus"),
		Sku: &network.LoadBalancerSku{
			Name: "Standard",
		},
		LoadBalancerPropertiesFormat: &network.LoadBalancerPropertiesFormat{
			ProvisioningState: network.ProvisioningStateSucceeded,
		},
	}
}
